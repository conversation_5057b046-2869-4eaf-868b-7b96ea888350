# Keepz Integration API Documentation

## Definition of Terms

- **Integrator:** Provider of POS systems.

## Environment URLs

- **Test Environment Base URL:** `https://gateway.dev.keepz.me/ecommerce-service`
- **Real Environment Base URL:** `https://gateway.keepz.me/ecommerce-service`

## Overview

The Integrator has the capability to integrate the Keepz payments system into their own system. Keepz offers the following method to achieve this:

1. The Integrator must create and register an order in the Keepz system.

2. After creating an order, the integrator can generate QR which is **unique per order** or just use static QR provided by Keepz representative which is **unique per beneficiary**. (Integrator can use only one of these ways and it must be agreed in advance which way integrator is going to use to register properly in Keepz system)

3. After scanning one of the QRs, users will view the order created by the Integrator, indicating the requested amount.

4. The user can either pay or cancel the order.

5. The final status will be communicated by the Keepz system to the Integrator system through the Callback URL. (Optional)

6. Additionally, the Integrator can check the status of a specific order.

All information exchange between the Integrator and the Keepz system occurs in a cryptographically encrypted format. In the initial phase, the Integrator is provided with a public key for encrypting information and a private key for decrypting received information.

## API Endpoints

### Create an Order

**Endpoint:** `POST /api/integrator/order`  
**Content-Type:** `application/json`

This method is used to create an individual order in the Keepz system.

#### Parameters

- **identifier** (UUID, Required) - Identifier of the integrator in the Keepz system (provided to the integrator by the Keepz representative).

- **encryptedData** (String, Required) - Request details encrypted with the integrator key. The encoded value must contain the following parameters:
  - **amount** (Double, Required) - Amount of the requested funds (in GEL).
  - **receiverId** (UUID, Required) - Identifier of the recipient of the money in the Keepz system (provided to the integrator by a Keepz representative).
  - **receiverType** (String, Required) - The type of receiver of the money in the Keepz system (must always be written as "BRANCH").
  - **integratorId** (UUID, Required) - Unique identifier of the integrator in the Keepz system (provided to the integrator by the Keepz representative).
  - **integratorOrderId** (UUID, Required) - Unique identifier of the order in the integrator system.

#### Request Example

```json
{
  "encryptedData": "string",
  "identifier": "3fa85f64-5717-4562-b3fc-2c963f66afa6"
}
```

**Encrypted data structure:**
```json
{
  "amount": 1.0,
  "receiverId": "0349ce8d-8061-4867-94a9-b9de0fb3fec6",
  "receiverType": "BRANCH",
  "integratorId": "ce8cc897-6963-4116-ac81-91323d172012",
  "integratorOrderId": "8ab2df0b-c749-4a68-bf3c-1179d17b83a4"
}
```

#### Response Parameters

- **encryptedData** (String) - Information encrypted with the Keepz key. The encoded value contains the following parameters:
  - **systemId** (String) - Unique identifier of the order in the Keepz system.
  - **integratorOrderId** (UUID) - Unique identifier of the order in the integrator system.
  - **urlForQR** (String) - URL which can be used to generate QR for created order (Returned if integrator generating QR per order)

#### Response Example

```json
{
  "encryptedData": "string"
}
```

**Encrypted data structure:**
```json
{
  "systemId": 1,
  "integratorOrderId": "8ab2df0b-c749-4a68-bf3c-1179d17b83a4"
}
```

### Check Order Status

**Endpoint:** `GET /api/integrator/order/status`

This method is used to check the status of an individual order.

#### Parameters

- **identifier** (UUID, Required) - Identifier of the integrator in the Keepz system (provided to the integrator by the Keepz representative).

- **encryptedData** (String, Required) - Request details encrypted with the key. The encrypted field must contain the following parameters:
  - **integratorId** (UUID, Required) - Unique identifier of the integrator in the Keepz system (provided to the integrator by the Keepz representative).
  - **integratorOrderId** (UUID, Required) - Unique identifier of the order in the integrator system.

#### Request Example

**Query Parameters:**
- `encryptedData`: "string"
- `identifier`: "3fa85f64-5717-4562-b3fc-2c963f66afa6"

**Encrypted data structure:**
```json
{
  "integratorId": "ce8cc897-6963-4116-ac81-91323d172012",
  "integratorOrderId": "8ab2df0b-c749-4a68-bf3c-1179d17b83a4"
}
```

#### Response Parameters

- **encryptedData** (String) - Response details encrypted with the Keepz key. The encoded value must contain the following parameters:
  - **integratorOrderId** (UUID) - Unique identifier of the order in the integrator system.
  - **status** (String) - The status of the order in the Keepz system.
    - **Possible statuses:** INITIAL, PROCESSING, SUCCESS, FAILED, CANCELED, EXPIRED, REFUNDED_BY_OPERATOR, REFUNDED_BY_INTEGRATOR, REFUNDED_BY_KEEPZ
  - **report** (String) - Order report (check). Sent only in case of the following statuses (SUCCESS, FAILED).

#### Response Example

```json
{
  "encryptedData": "string"
}
```

**Encrypted data structure:**
```json
{
  "status": "EXPIRED",
  "integratorOrderId": "8ab2df0b-c749-4a68-bf3c-1179d17b83a4",
  "report": "Report"
}
```

### Order Cancellation

**Endpoint:** `DELETE /api/integrator/order/cancel`

An individual order can be canceled through this method.

#### Parameters

- **identifier** (UUID, Required) - Identifier of the integrator in the Keepz system (provided to the integrator by the Keepz representative).

- **encryptedData** (String, Required) - Request details encrypted with the key. The encrypted field must contain the following parameters:
  - **integratorId** (UUID, Required) - Unique identifier of the integrator in the Keepz system (provided to the integrator by the Keepz representative).
  - **integratorOrderId** (UUID, Required) - Unique identifier of the order in the integrator system.

#### Request Example

**Query Parameters:**
- `encryptedData`: "string"
- `identifier`: "3fa85f64-5717-4562-b3fc-2c963f66afa6"

**Encrypted data structure:**
```json
{
  "integratorId": "ce8cc897-6963-4116-ac81-91323d172012",
  "integratorOrderId": "8ab2df0b-c749-4a68-bf3c-1179d17b83a4"
}
```

#### Response Parameters

- **encryptedData** (String) - Response details encrypted with the Keepz key. The encoded value must contain the following parameters:
  - **integratorOrderId** (UUID) - Unique identifier of the order in the integrator system.
  - **status** (String) - The status of the order in the Keepz system. In case of successful cancellation of the order, the value of status should be "CANCELLED".

#### Response Example

```json
{
  "encryptedData": "string"
}
```

**Encrypted data structure:**
```json
{
  "status": "CANCELLED",
  "integratorOrderId": "8ab2df0b-c749-4a68-bf3c-1179d17b83a4"
}
```

## Callback URL Status Delivery

**Note:** Delivery of the status to the Callback URL is possible only if the integrator has called the order creation command!

The Keepz system can provide the final status to the integrator's system. For this, the integrator needs to provide the Keepz representative with the URL in advance, where they will receive the final status. The URL should receive a request of type POST, Content-Type should be application/json, and should contain the following parameters:

### Callback Parameters

- **encryptedData** (String) - Request details encrypted with Keepz key. The encoded value must contain the following parameters:
  - **systemId** (Long) - Unique identifier of the order in the Keepz system.
  - **amount** (Double) - Amount of the requested funds (in GEL).
  - **receiverId** (UUID) - Identifier of the recipient of the money in the Keepz system (provided to the integrator by a Keepz representative).
  - **receiverType** (String) - The type of receiver of the money in the Keepz system.
  - **integratorId** (UUID) - Unique identifier of the integrator in the Keepz system (provided to the integrator by the Keepz representative).
  - **integratorOrderId** (UUID) - Unique identifier of the order in the integrator system.
  - **status** (String) - The status of the order in the Keepz system.

### Callback Example

```json
{
  "encryptedData": "string"
}
```

**Encrypted data structure:**
```json
{
  "systemId": 1,
  "amount": 1.0,
  "receiverId": "0349ce8d-8061-4867-94a9-b9de0fb3fec6",
  "receiverType": "BRANCH",
  "integratorId": "ce8cc897-6963-4116-ac81-91323d172012",
  "integratorOrderId": "8ab2df0b-c749-4a68-bf3c-1179d17b83a4",
  "status": "EXPIRED"
}
```