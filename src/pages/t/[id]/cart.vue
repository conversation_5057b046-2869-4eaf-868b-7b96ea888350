<script setup lang="ts">
import { Alert } from "flowbite-vue";
import { ref, computed } from 'vue';

const { restaurant } = useTable();
const { addOrder, pending, orderedDishesPrice, getOrders } = useOrder();
const { cartDishes, cartDishesPrice, clearCart, getCart } = useCart();
const ordered = ref(false);
const { capitalizeFirstLetter } = useHelpers();
const userLocation = ref(null);
const isLocationRequired = ref(true);

const handleLocationSelected = (location) => {
  userLocation.value = location;
};

const placeOrderAndClearCart = async () => {
  if (isLocationRequired.value && !userLocation.value) {
    $warningToast('Please share your location for delivery');
    return;
  }

  const data = await addOrder({
    location: userLocation.value
  });

  if (data.value) {
    await getOrders();
    await clearCart();
    ordered.value = true;
  }
};

const feeAmont = computed((): number => {
  if (restaurant.value?.fee) {
    return Number((restaurant.value?.fee / 100) * cartDishesPrice.value);
  } else {
    return 0;
  }
});

const calculatePaymentSummaries = () => {
  const cartTotal = cartDishes.value
    ? cartDishes.value.reduce((sum, dish) => {
        // Check if there is a promo and if newPrice is set in the first promo
        const effectivePrice =
          dish.promos &&
          dish.promos.length > 0 &&
          dish.promos[0] &&
          "newPrice" in dish.promos[0]
            ? dish.promos[0].newPrice
            : dish.price;

        return sum + (effectivePrice as number) * (dish.quantity ?? 0);
      }, 0)
    : 0;

  const orderedTotal =
    orderedDishesPrice.value +
    (restaurant.value?.fee
      ? Number((restaurant.value?.fee / 100) * orderedDishesPrice.value)
      : 0);
  const fee = restaurant.value?.fee
    ? Number((restaurant.value?.fee / 100) * cartTotal)
    : 0;
  const grandTotal = cartTotal + orderedTotal + fee;

  return [
    {
      title: "subTotal",
      price: cartTotal.toFixed(2),
    },
    {
      title: "previouslyOrdered",
      price: orderedTotal.toFixed(2),
    },
    {
      title: "serviceFee",
      price: fee.toFixed(2),
    },
    {
      title: "total",
      price: grandTotal.toFixed(2),
    },
  ];
};

const PaymentSummaries = ref(calculatePaymentSummaries());

watch([cartDishes, orderedDishesPrice, feeAmont], () => {
  PaymentSummaries.value = calculatePaymentSummaries();
});
const loading = useLoading();

onMounted(() => {
  loading.value = false;
});
</script>

<template>
  <NuxtLayout name="table-layout">
    <template #headerTitle>
      {{ capitalizeFirstLetter($t("basket")) }}
    </template>
    <main>
      <section v-if="!ordered">
        <ItemCards
          :items="cartDishes?.map(dish => ({ ...dish, sound: '' })) ?? []"
          :alert="$t('noDishes')"
          :showImage="false"
        />
      </section>

      <!-- Add Location Picker -->
      <section class="mt-6" v-if="cartDishesPrice && !ordered">
        <h3 class="text-lg font-semibold mb-4">{{ $t('deliveryLocation') }}</h3>
        <LocationPicker @location-selected="handleLocationSelected" />
      </section>

      <section class="mt-6" v-if="cartDishesPrice">
        <ul>
          <PaymentSummary
            v-for="(summary, index) in PaymentSummaries"
            :key="index"
            :title="summary.title"
            :price="summary.price"
          />
        </ul>
        <Button
          @click="placeOrderAndClearCart"
          :loading="pending"
          class="bg-velvet w-full mt-4 py-4 font-bold flex justify-center"
        >
          <span class="font-semibold tracking-widest" v-if="!pending">
            {{ $t("order") }}
          </span>
        </Button>
      </section>
      <section v-if="ordered">
        <Alert type="success">{{ $t("orderCreatedSuccessfully") }}</Alert>
      </section>
    </main>
  </NuxtLayout>
</template>
