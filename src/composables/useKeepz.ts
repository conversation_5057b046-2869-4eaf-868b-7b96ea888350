import { ref } from 'vue';

export function useKeepz() {
  const config = useRuntimeConfig();
  const keepzPending = ref(false);
  const { $warningToast } = useNuxtApp();
  const { t } = useI18n();

  const createKeepzOrder = async (amount: number, integratorOrderId: string) => {
    keepzPending.value = true;
    try {
      // This would be replaced with actual encryption logic
      const encryptedData = JSON.stringify({
        amount: amount,
        receiverId: config.public.keepzReceiverId,
        receiverType: "BRANCH",
        integratorId: config.public.keepzIntegratorId,
        integratorOrderId: integratorOrderId
      });

      const response = await fetch(`${config.public.keepzBaseUrl}/api/integrator/order`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          encryptedData: encryptedData, // In production, this would be properly encrypted
          identifier: config.public.keepzIdentifier
        })
      });

      const data = await response.json();
      
      // In production, this would be properly decrypted
      const decryptedData = JSON.parse(data.encryptedData);
      
      keepzPending.value = false;
      return {
        success: true,
        systemId: decryptedData.systemId,
        integratorOrderId: decryptedData.integratorOrderId,
        urlForQR: decryptedData.urlForQR
      };
    } catch (error) {
      keepzPending.value = false;
      $warningToast(t("keepzPaymentError"));
      return { success: false, error };
    }
  };

  return {
    createKeepzOrder,
    keepzPending
  };
}