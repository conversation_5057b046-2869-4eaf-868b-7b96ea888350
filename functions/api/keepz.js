// functions/api/keepz.js
export async function onRequestPost(context) {
  const { request, env } = context;

  try {
    // Environment variables (set in Cloudflare Pages dashboard)
    const KEEPZ_INTEGRATOR_ID = env.KEEPZ_INTEGRATOR_ID || "05382470-0ddb-4e36-8f23-4939f3b81ec9";
    const KEEPZ_IDENTIFIER = env.KEEPZ_IDENTIFIER || env.KEEPZ_INTEGRATOR_ID || "05382470-0ddb-4e36-8f23-4939f3b81ec9";
    const KEEPZ_RECEIVER_ID = env.KEEPZ_RECEIVER_ID || "0349ce8d-8061-4867-94a9-b9de0fb3fec6";
    const ORDER_AMOUNT = env.ORDER_AMOUNT || "25.50";
    const KEEPZ_BASE_URL = env.KEEPZ_BASE_URL || "https://gateway.dev.keepz.me/ecommerce-service";

    // RSA Keys from environment variables
    const KEEPZ_PUBLIC_KEY = env.KEEPZ_PUBLIC_KEY;
    const KEEPZ_PRIVATE_KEY = env.KEEPZ_PRIVATE_KEY;

    // Validate required environment variables
    if (!KEEPZ_PUBLIC_KEY) {
      return new Response('Error: KEEPZ_PUBLIC_KEY environment variable is required', { status: 400 });
    }

    if (!KEEPZ_PRIVATE_KEY) {
      return new Response('Error: KEEPZ_PRIVATE_KEY environment variable is required', { status: 400 });
    }

    // Parse request body to get order details (optional override)
    let orderDetails = {};
    try {
      const body = await request.json();
      orderDetails = body;
    } catch (error) {
      // Use default values if no valid JSON body
    }

    // Generate UUID using crypto.randomUUID()
    function generateUUID() {
      return crypto.randomUUID();
    }

    // Convert base64 key to PEM format
    function convertToPemFormat(base64Key, keyType = 'PUBLIC') {
      const pemHeader = keyType === 'PUBLIC' ? '-----BEGIN PUBLIC KEY-----' : '-----BEGIN PRIVATE KEY-----';
      const pemFooter = keyType === 'PUBLIC' ? '-----END PUBLIC KEY-----' : '-----END PRIVATE KEY-----';
      const pemBody = base64Key.match(/.{1,64}/g).join('\n');
      return `${pemHeader}\n${pemBody}\n${pemFooter}`;
    }

    // Encrypt data using RSA public key (Web Crypto API)
    async function encryptData(data, publicKeyPem) {
      try {
        // Import the public key
        const publicKey = await crypto.subtle.importKey(
          'spki',
          pemToArrayBuffer(publicKeyPem),
          {
            name: 'RSA-OAEP',
            hash: 'SHA-256'
          },
          false,
          ['encrypt']
        );

        // Encrypt the data
        const encoder = new TextEncoder();
        const dataBuffer = encoder.encode(data);
        const encrypted = await crypto.subtle.encrypt(
          {
            name: 'RSA-OAEP'
          },
          publicKey,
          dataBuffer
        );

        // Convert to base64
        return arrayBufferToBase64(encrypted);
      } catch (error) {
        console.error('Encryption failed:', error.message);
        throw error;
      }
    }

    // Decrypt data using RSA private key (Web Crypto API)
    async function decryptData(encryptedData, privateKeyPem) {
      try {
        // Import the private key
        const privateKey = await crypto.subtle.importKey(
          'pkcs8',
          pemToArrayBuffer(privateKeyPem),
          {
            name: 'RSA-OAEP',
            hash: 'SHA-256'
          },
          false,
          ['decrypt']
        );

        // Convert base64 to ArrayBuffer
        const encryptedBuffer = base64ToArrayBuffer(encryptedData);

        // Decrypt the data
        const decrypted = await crypto.subtle.decrypt(
          {
            name: 'RSA-OAEP'
          },
          privateKey,
          encryptedBuffer
        );

        // Convert back to string
        const decoder = new TextDecoder();
        return decoder.decode(decrypted);
      } catch (error) {
        console.error('Decryption failed:', error.message);
        throw error;
      }
    }

    // Helper function to convert PEM to ArrayBuffer
    function pemToArrayBuffer(pem) {
      const b64Lines = pem.replace(/-----[^-]+-----/g, '').replace(/\s/g, '');
      return base64ToArrayBuffer(b64Lines);
    }

    // Helper function to convert base64 to ArrayBuffer
    function base64ToArrayBuffer(base64) {
      const binaryString = atob(base64);
      const bytes = new Uint8Array(binaryString.length);
      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }
      return bytes.buffer;
    }

    // Helper function to convert ArrayBuffer to base64
    function arrayBufferToBase64(buffer) {
      const bytes = new Uint8Array(buffer);
      let binary = '';
      for (let i = 0; i < bytes.byteLength; i++) {
        binary += String.fromCharCode(bytes[i]);
      }
      return btoa(binary);
    }

    // Create the order data object
    const orderData = {
      amount: parseFloat(orderDetails.amount || ORDER_AMOUNT),
      receiverId: orderDetails.receiverId || KEEPZ_RECEIVER_ID,
      receiverType: orderDetails.receiverType || "BRANCH",
      integratorId: orderDetails.integratorId || KEEPZ_INTEGRATOR_ID,
      integratorOrderId: orderDetails.integratorOrderId || generateUUID()
    };

    console.log('Order data to be encrypted:', JSON.stringify(orderData, null, 2));

    // Convert JSON to string and encrypt it
    const jsonData = JSON.stringify(orderData);
    const publicKeyPem = convertToPemFormat(KEEPZ_PUBLIC_KEY, 'PUBLIC');

    console.log('Using public key for encryption');

    let encryptedData;
    try {
      encryptedData = await encryptData(jsonData, publicKeyPem);
      console.log('Successfully encrypted data');
      console.log('Encrypted data length:', encryptedData.length);
    } catch (error) {
      console.error('Failed to encrypt data:', error.message);
      return new Response(`Failed to encrypt data: ${error.message}`, { status: 500 });
    }

    // Prepare the API request payload
    const requestPayload = {
      encryptedData: encryptedData,
      identifier: KEEPZ_IDENTIFIER
    };

    console.log('Request payload prepared');
    console.log('Identifier:', KEEPZ_IDENTIFIER);

    // Make the API request
    const apiUrl = `${KEEPZ_BASE_URL}/api/integrator/order`;
    console.log('Making API call to:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(requestPayload)
    });

    const responseData = await response.text();

    console.log('Response Status:', response.status);
    console.log('Response Headers:', JSON.stringify([...response.headers.entries()]));
    console.log('Response Body:', responseData);

    if (response.status === 200 || response.status === 201) {
      try {
        const responseObj = JSON.parse(responseData);
        console.log('\nAPI call successful!');

        if (responseObj.encryptedData) {
          console.log('Encrypted response data received');

          // Decrypt the response using private key
          const privateKeyPem = convertToPemFormat(KEEPZ_PRIVATE_KEY, 'PRIVATE');

          try {
            const decryptedResponse = await decryptData(responseObj.encryptedData, privateKeyPem);
            console.log('Successfully decrypted response');
            console.log('Decrypted response:', JSON.stringify(JSON.parse(decryptedResponse), null, 2));

            const decryptedObj = JSON.parse(decryptedResponse);
            
            const result = {
              success: true,
              systemId: decryptedObj.systemId,
              integratorOrderId: decryptedObj.integratorOrderId,
              urlForQR: decryptedObj.urlForQR,
              originalResponse: decryptedObj
            };

            if (decryptedObj.systemId) {
              console.log('\nOrder created successfully!');
              console.log('System ID:', decryptedObj.systemId);
              console.log('Integrator Order ID:', decryptedObj.integratorOrderId);
              if (decryptedObj.urlForQR) {
                console.log('QR URL:', decryptedObj.urlForQR);
              }
            }

            return new Response(JSON.stringify(result, null, 2), {
              status: 200,
              headers: {
                'Content-Type': 'application/json'
              }
            });
          } catch (decryptError) {
            console.error('Failed to decrypt response:', decryptError.message);
            return new Response(`Failed to decrypt response: ${decryptError.message}`, { status: 500 });
          }
        } else {
          console.log('No encrypted data in response');
          return new Response(JSON.stringify({
            success: true,
            message: 'No encrypted data in response',
            response: responseObj
          }, null, 2), {
            status: 200,
            headers: {
              'Content-Type': 'application/json'
            }
          });
        }
      } catch (parseError) {
        console.log('Response is not valid JSON:', parseError.message);
        return new Response(`Response is not valid JSON: ${parseError.message}`, { status: 500 });
      }
    } else {
      console.error('API call failed with status:', response.status);
      return new Response(`API call failed with status: ${response.status}\n${responseData}`, { 
        status: response.status 
      });
    }

  } catch (error) {
    console.error('Function error:', error.message);
    return new Response(`Function error: ${error.message}`, { status: 500 });
  }
}

// Optional: Handle other HTTP methods
export async function onRequest(context) {
  const { request } = context;
  
  if (request.method !== 'POST') {
    return new Response('Method not allowed. Only POST requests are supported.', { 
      status: 405,
      headers: {
        'Allow': 'POST'
      }
    });
  }
  
  // This shouldn't be reached due to onRequestPost, but just in case
  return new Response('Use POST method', { status: 405 });
}