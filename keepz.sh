#!/bin/bash

# =============================================================================
# Keepz API Integration Example Script
# =============================================================================
# This script demonstrates how to integrate with the Keepz payment system
# Author: Integration Team
# Version: 1.0
# =============================================================================

set -euo pipefail  # Exit on error, undefined vars, pipe failures

# =============================================================================
# CONFIGURATION
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Script directory
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
LOG_FILE="${SCRIPT_DIR}/keepz_api.log"
RESPONSE_DIR="${SCRIPT_DIR}/responses"

# Create responses directory
mkdir -p "$RESPONSE_DIR"

# =============================================================================
# LOGGING FUNCTIONS
# =============================================================================

log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" | tee -a "$LOG_FILE"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
}

# =============================================================================
# ENVIRONMENT SETUP
# =============================================================================

setup_environment() {
    log_info "Setting up environment variables..."

    # Load environment variables from .env file if it exists
    if [[ -f "${SCRIPT_DIR}/.env" ]]; then
        source "${SCRIPT_DIR}/.env"
        log_info "Loaded environment variables from .env file"
    fi

    # Set default values if not provided
    export KEEPZ_ENV="${KEEPZ_ENV:-dev}"
    export KEEPZ_BASE_URL_DEV="${KEEPZ_BASE_URL_DEV:-https://gateway.dev.keepz.me/ecommerce-service}"
    export KEEPZ_BASE_URL_PROD="${KEEPZ_BASE_URL_PROD:-https://gateway.keepz.me/ecommerce-service}"

    # Example credentials (replace with actual values from Keepz representative)
    export KEEPZ_INTEGRATOR_ID="${KEEPZ_INTEGRATOR_ID:-ce8cc897-6963-4116-ac81-91323d172012}"
    export KEEPZ_IDENTIFIER="${KEEPZ_IDENTIFIER:-3fa85f64-5717-4562-b3fc-2c963f66afa6}"
    export KEEPZ_RECEIVER_ID="${KEEPZ_RECEIVER_ID:-0349ce8d-8061-4867-94a9-b9de0fb3fec6}"

    # Order configuration
    export ORDER_AMOUNT="${ORDER_AMOUNT:-15.75}"
    export INTEGRATOR_ORDER_ID="${INTEGRATOR_ORDER_ID:-$(generate_uuid)}"

    log_info "Environment: $KEEPZ_ENV"
    log_info "Order Amount: $ORDER_AMOUNT GEL"
    log_info "Order ID: $INTEGRATOR_ORDER_ID"
}

# =============================================================================
# UTILITY FUNCTIONS
# =============================================================================

generate_uuid() {
    if command -v uuidgen >/dev/null 2>&1; then
        uuidgen | tr '[:upper:]' '[:lower:]'
    else
        # Fallback UUID generation (not cryptographically secure)
        python3 -c "import uuid; print(str(uuid.uuid4()))" 2>/dev/null || \
        echo "$(date +%s)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)-$(shuf -i 1000-9999 -n 1)"
    fi
}

get_base_url() {
    if [[ "$KEEPZ_ENV" == "prod" ]]; then
        echo "$KEEPZ_BASE_URL_PROD"
    else
        echo "$KEEPZ_BASE_URL_DEV"
    fi
}

validate_json() {
    local file="$1"
    if [[ -f "$file" ]] && jq empty "$file" 2>/dev/null; then
        return 0
    else
        return 1
    fi
}

# =============================================================================
# ENCRYPTION FUNCTIONS (MOCK IMPLEMENTATION)
# =============================================================================
# Note: In production, implement actual RSA encryption with Keepz-provided keys

encrypt_data() {
    local data="$1"
    log_info "Encrypting data (mock implementation)"

    # For mock implementation, just use base64 encoding
    # In production, use proper RSA encryption with public key
    echo -n "$data" | base64 | tr -d '\n'
}

decrypt_data() {
    local encrypted_data="$1"
    log_info "Decrypting data (mock implementation)"

    # Mock decryption: Just decode base64
    # In production, use proper RSA decryption with private key
    echo "$encrypted_data" | base64 -d 2>/dev/null || echo "{\"error\":\"decryption_failed\"}"
}

# =============================================================================
# API FUNCTIONS
# =============================================================================

create_order() {
    local amount="$1"
    local order_id="$2"

    log_info "Creating order with amount: $amount GEL, ID: $order_id"

    # Prepare order data
    local order_data=$(cat <<EOF
{
  "amount": $amount,
  "receiverId": "$KEEPZ_RECEIVER_ID",
  "receiverType": "BRANCH",
  "integratorId": "$KEEPZ_INTEGRATOR_ID",
  "integratorOrderId": "$order_id"
}
EOF
)

    log_info "Order data prepared: $order_data"

    # Validate JSON before encryption
    if ! echo "$order_data" | jq empty 2>/dev/null; then
        log_error "Invalid JSON in order data: $order_data"
        return 1
    fi

    # Encrypt the order data
    local encrypted_data=$(encrypt_data "$order_data")

    # Use jq to properly escape the JSON
    local request_payload=$(jq -n \
                           --arg enc "$encrypted_data" \
                           --arg id "$KEEPZ_IDENTIFIER" \
                           '{encryptedData: $enc, identifier: $id}')

    # Make API call
    local response_file="${RESPONSE_DIR}/create_order_${order_id}.json"
    local base_url=$(get_base_url)

    log_info "Making API call to: ${base_url}/api/integrator/order"

    local http_code=$(curl -s -w "%{http_code}" -X POST "${base_url}/api/integrator/order" \
        -H "Content-Type: application/json" \
        -H "Accept: application/json" \
        -H "User-Agent: Keepz-Integrator/1.0" \
        -d "$request_payload" \
        -o "$response_file")

    log_info "HTTP Status Code: $http_code"

    if [[ "$http_code" == "200" ]] || [[ "$http_code" == "201" ]]; then
        log_success "Order created successfully"

        if validate_json "$response_file"; then
            log_info "Response saved to: $response_file"

            # Extract and decrypt response
            local encrypted_response=$(jq -r '.encryptedData' "$response_file" 2>/dev/null || echo "")
            if [[ -n "$encrypted_response" && "$encrypted_response" != "null" ]]; then
                local decrypted_response=$(decrypt_data "$encrypted_response")
                log_info "Decrypted response: $decrypted_response"

                # Extract system ID if available
                local system_id=$(echo "$decrypted_response" | jq -r '.systemId' 2>/dev/null || echo "")
                if [[ -n "$system_id" && "$system_id" != "null" ]]; then
                    log_success "Keepz System ID: $system_id"
                    echo "$system_id" > "${RESPONSE_DIR}/system_id_${order_id}.txt"
                fi
            fi
        else
            log_warning "Invalid JSON response received"
        fi

        return 0
    else
        log_error "Failed to create order. HTTP Status: $http_code"
        [[ -f "$response_file" ]] && log_error "Response: $(cat "$response_file")"
        return 1
    fi
}

check_order_status() {
    local order_id="$1"

    log_info "Checking status for order: $order_id"

    # Prepare status check data
    local status_data=$(cat <<EOF
{
  "integratorId": "$KEEPZ_INTEGRATOR_ID",
  "integratorOrderId": "$order_id"
}
EOF
)

    # Encrypt the status data
    local encrypted_data=$(encrypt_data "$status_data")

    # Use jq to properly escape the query parameters
    local enc_param=$(jq -rn --arg enc "$encrypted_data" '$enc | @uri')
    local id_param=$(jq -rn --arg id "$KEEPZ_IDENTIFIER" '$id | @uri')

    # Make API call
    local response_file="${RESPONSE_DIR}/status_${order_id}.json"
    local base_url=$(get_base_url)

    log_info "Checking order status..."

    local http_code=$(curl -s -w "%{http_code}" -X GET \
        "${base_url}/api/integrator/order/status?encryptedData=${enc_param}&identifier=${id_param}" \
        -H "Accept: application/json" \
        -H "User-Agent: Keepz-Integrator/1.0" \
        -o "$response_file")

    log_info "HTTP Status Code: $http_code"

    if [[ "$http_code" == "200" ]]; then
        log_success "Status check completed"

        if validate_json "$response_file"; then
            local encrypted_response=$(jq -r '.encryptedData' "$response_file" 2>/dev/null || echo "")
            if [[ -n "$encrypted_response" && "$encrypted_response" != "null" ]]; then
                local decrypted_response=$(decrypt_data "$encrypted_response")
                log_info "Decrypted response: $decrypted_response"

                local status=$(echo "$decrypted_response" | jq -r '.status' 2>/dev/null || echo "UNKNOWN")
                log_info "Order Status: $status"

                # Save status to file
                echo "$status" > "${RESPONSE_DIR}/status_${order_id}.txt"

                return 0
            fi
        fi
    else
        log_error "Failed to check order status. HTTP Status: $http_code"
        return 1
    fi
}

cancel_order() {
    local order_id="$1"

    log_info "Cancelling order: $order_id"

    # Prepare cancellation data
    local cancel_data=$(cat <<EOF
{
  "integratorId": "$KEEPZ_INTEGRATOR_ID",
  "integratorOrderId": "$order_id"
}
EOF
)

    # Encrypt the cancellation data
    local encrypted_data=$(encrypt_data "$cancel_data")

    # Use jq to properly escape the query parameters
    local enc_param=$(jq -rn --arg enc "$encrypted_data" '$enc | @uri')
    local id_param=$(jq -rn --arg id "$KEEPZ_IDENTIFIER" '$id | @uri')

    # Make API call
    local response_file="${RESPONSE_DIR}/cancel_${order_id}.json"
    local base_url=$(get_base_url)

    log_info "Cancelling order..."

    local http_code=$(curl -s -w "%{http_code}" -X DELETE \
        "${base_url}/api/integrator/order/cancel?encryptedData=${enc_param}&identifier=${id_param}" \
        -H "Accept: application/json" \
        -H "User-Agent: Keepz-Integrator/1.0" \
        -o "$response_file")

    log_info "HTTP Status Code: $http_code"

    if [[ "$http_code" == "200" ]]; then
        log_success "Order cancellation completed"

        if validate_json "$response_file"; then
            local encrypted_response=$(jq -r '.encryptedData' "$response_file" 2>/dev/null || echo "")
            if [[ -n "$encrypted_response" && "$encrypted_response" != "null" ]]; then
                local decrypted_response=$(decrypt_data "$encrypted_response")
                log_info "Decrypted response: $decrypted_response"

                local status=$(echo "$decrypted_response" | jq -r '.status' 2>/dev/null || echo "UNKNOWN")
                log_info "Cancellation Status: $status"

                return 0
            fi
        fi
    else
        log_error "Failed to cancel order. HTTP Status: $http_code"
        return 1
    fi
}

# =============================================================================
# MAIN WORKFLOW FUNCTIONS
# =============================================================================

run_order_workflow() {
    local amount="${1:-$ORDER_AMOUNT}"
    local order_id="${2:-$INTEGRATOR_ORDER_ID}"

    log_info "Starting order workflow for amount: $amount GEL"

    # Step 1: Create order
    if create_order "$amount" "$order_id"; then
        log_success "✓ Order created successfully"

        # Step 2: Wait a moment (simulate processing time)
        log_info "Waiting 2 seconds before status check..."
        sleep 2

        # Step 3: Check order status
        if check_order_status "$order_id"; then
            log_success "✓ Order status checked successfully"

            # Step 4: Optionally cancel the order (for demo purposes)
            read -p "Do you want to cancel this order? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                if cancel_order "$order_id"; then
                    log_success "✓ Order cancelled successfully"
                else
                    log_error "✗ Failed to cancel order"
                fi
            else
                log_info "Order cancellation skipped"
            fi
        else
            log_error "✗ Failed to check order status"
        fi
    else
        log_error "✗ Failed to create order"
        return 1
    fi
}

# =============================================================================
# INTERACTIVE MENU
# =============================================================================

show_menu() {
    echo
    echo "=== Keepz API Integration Demo ==="
    echo "1. Run complete order workflow"
    echo "2. Create order only"
    echo "3. Check order status"
    echo "4. Cancel order"
    echo "5. View configuration"
    echo "6. Clean up response files"
    echo "7. Exit"
    echo
}

interactive_mode() {
    while true; do
        show_menu
        read -p "Select an option (1-7): " choice

        case $choice in
            1)
                log_info "Running complete order workflow..."
                run_order_workflow
                ;;
            2)
                read -p "Enter order amount (default: $ORDER_AMOUNT): " amount
                amount="${amount:-$ORDER_AMOUNT}"
                order_id=$(generate_uuid)
                create_order "$amount" "$order_id"
                ;;
            3)
                read -p "Enter order ID to check: " order_id
                if [[ -n "$order_id" ]]; then
                    check_order_status "$order_id"
                else
                    log_error "Order ID is required"
                fi
                ;;
            4)
                read -p "Enter order ID to cancel: " order_id
                if [[ -n "$order_id" ]]; then
                    cancel_order "$order_id"
                else
                    log_error "Order ID is required"
                fi
                ;;
            5)
                echo "=== Current Configuration ==="
                echo "Environment: $KEEPZ_ENV"
                echo "Base URL: $(get_base_url)"
                echo "Integrator ID: $KEEPZ_INTEGRATOR_ID"
                echo "Receiver ID: $KEEPZ_RECEIVER_ID"
                echo "Default Amount: $ORDER_AMOUNT GEL"
                ;;
            6)
                read -p "Are you sure you want to delete all response files? (y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    rm -f "${RESPONSE_DIR}"/*
                    log_info "Response files cleaned up"
                fi
                ;;
            7)
                log_info "Exiting..."
                exit 0
                ;;
            *)
                log_error "Invalid option. Please try again."
                ;;
        esac

        echo
        read -p "Press Enter to continue..."
    done
}

# =============================================================================
# MAIN EXECUTION
# =============================================================================

main() {
    # Clear screen and show header
    clear
    echo "==============================================================================="
    echo "                      Keepz API Integration Example"
    echo "==============================================================================="
    echo

    # Setup environment
    setup_environment

    # Check dependencies
    log_info "Checking dependencies..."
    for cmd in curl jq; do
        if ! command -v "$cmd" >/dev/null 2>&1; then
            log_error "Required command '$cmd' not found. Please install it first."
            exit 1
        fi
    done
    log_success "All dependencies are available"

    # Check if running in production
    if [[ "$KEEPZ_ENV" == "prod" ]]; then
        log_warning "WARNING: You are using PRODUCTION environment!"
        read -p "Are you sure you want to proceed? (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "Operation cancelled"
            exit 0
        fi
    fi

    # Run based on command line arguments
    if [[ $# -eq 0 ]]; then
        # No arguments - run interactive mode
        interactive_mode
    else
        # Command line arguments provided
        case "$1" in
            "create")
                amount="${2:-$ORDER_AMOUNT}"
                order_id="${3:-$(generate_uuid)}"
                create_order "$amount" "$order_id"
                ;;
            "status")
                if [[ -n "$2" ]]; then
                    check_order_status "$2"
                else
                    log_error "Order ID is required for status check"
                    exit 1
                fi
                ;;
            "cancel")
                if [[ -n "$2" ]]; then
                    cancel_order "$2"
                else
                    log_error "Order ID is required for cancellation"
                    exit 1
                fi
                ;;
            "workflow")
                amount="${2:-$ORDER_AMOUNT}"
                order_id="${3:-$(generate_uuid)}"
                run_order_workflow "$amount" "$order_id"
                ;;
            *)
                echo "Usage: $0 [create|status|cancel|workflow] [amount] [order_id]"
                echo "  create [amount] [order_id] - Create a new order"
                echo "  status [order_id]          - Check order status"
                echo "  cancel [order_id]          - Cancel an order"
                echo "  workflow [amount] [order_id] - Run complete workflow"
                echo ""
                echo "Run without arguments for interactive mode"
                exit 1
                ;;
        esac
    fi
}

# =============================================================================
# SCRIPT EXECUTION
# =============================================================================

# Trap to handle script interruption
trap 'log_warning "Script interrupted by user"; exit 130' INT

# Run main function with all arguments
main "$@"
